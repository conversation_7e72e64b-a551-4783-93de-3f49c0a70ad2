package com.kan.config;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.kan.modules.sys.utils.OssProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * S3配置类
 * 
 * <AUTHOR>
 */
@Configuration
public class S3Config {

    @Autowired
    private OssProperties ossProperties;

    @Bean
    public AmazonS3Client amazonS3Client() {
        // 创建AWS凭证
        AWSCredentials credentials = new BasicAWSCredentials(
            ossProperties.getAccessKey(), 
            ossProperties.getSecretKey()
        );

        // 创建S3客户端
        return (AmazonS3Client) AmazonS3ClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(credentials))
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(
                    ossProperties.getEndpoint(), 
                    "us-east-1" // 默认区域，对于MinIO等S3兼容存储通常不重要
                ))
                .withPathStyleAccessEnabled(true) // 对于MinIO等S3兼容存储，通常需要启用路径样式访问
                .build();
    }
}
