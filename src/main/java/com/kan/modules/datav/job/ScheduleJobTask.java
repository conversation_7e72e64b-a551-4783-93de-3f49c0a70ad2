package com.kan.modules.datav.job;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kan.common.utils.PlatHttpUtil;
import com.kan.modules.datav.CarLineMapper;
import com.kan.modules.datav.dto.*;
import com.kan.modules.datav.mapper.DriverInfoDayNumMapper;
import com.kan.modules.datav.utils.CarTypeNumMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.kan.modules.datav.service.impl.DataBoardServiceImpl.*;

@Component
@Configuration      //1.主要用于标记配置类，兼备Component的效果。
//@EnableScheduling   // 2.开启定时任务 - 已禁用
@Slf4j
public class ScheduleJobTask {


    @Resource
    CarLineMapper carLineMapper;
    @Resource
    CarTypeNumMapper carTypeNumMapper;
    @Resource
    MongoTemplate mongoTemplate;
    @Resource
    DriverInfoDayNumMapper driverInfoDayNumMapper;

//    @Scheduled(cron = "0 0 * * * ?")
    public void getData() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH");
        //登录认证
        String url = "http://36.133.68.39:12056/api/v1/basic/key?username=admin&password=admin";
        String s = PlatHttpUtil.sendGetParams(url, null, "UTF-8", null);
        //获取登录key
        RespJsonObj jsonObject = JSONObject.parseObject(s, RespJsonObj.class);
        //出租车总数
        int beiBeiCount = 0;
        int baNanCount = 0;
        int yuBeiCount = 0;
        int chuZuCount = 0;
        List<String> beiBeiTherid = new ArrayList<>();
        List<String> baNanTherid = new ArrayList<>();
        List<String> yuBeiTherid = new ArrayList<>();
        List<String> chuZuTherid = new ArrayList<>();
        if (jsonObject.getErrorcode().equals("200")) {
            //获取设备列表
            String devicesUrl = "http://36.133.68.39:12056/api/v1/basic/devices?key=" + jsonObject.getData().getKey();
            String devicesResp = PlatHttpUtil.sendGetParams(devicesUrl, null, "utf-8", null);
            DevicesResp devices = JSONObject.parseObject(devicesResp, DevicesResp.class);
            List<DevicesResp.RespJsonObjBuilder> data = devices.getData();
            int beiBeiYouCar = 0;
            int beiBeiDianCar = 0;
            int yuBeiYouCar = 0;
            int yuBeiDianCar = 0;
            int baNanYouCar = 0;
            int baNanDianCar = 0;
            int chuZuYouCar = 0;
            int chuZuDianCar = 0;
            for (DevicesResp.RespJsonObjBuilder datum : data) {
                if (datum.getGroupid().equals(3)) {
                    if (datum.getCarlicence().length() == 8) {
                        beiBeiDianCar++;
                    } else {
                        beiBeiYouCar++;
                    }
                    beiBeiTherid.add(datum.getTerid());
                    beiBeiCount++;
                } else if (datum.getGroupid().equals(5)) {
                    if (datum.getCarlicence().length() == 8) {
                        baNanDianCar++;
                    } else {
                        baNanYouCar++;
                    }
                    baNanTherid.add(datum.getTerid());
                    baNanCount++;
                } else if (datum.getGroupid().equals(6)) {
                    if (datum.getCarlicence().length() == 8) {
                        yuBeiDianCar++;
                    } else {
                        yuBeiYouCar++;
                    }
                    yuBeiTherid.add(datum.getTerid());
                    yuBeiCount++;
                } else if (datum.getGroupid().equals(9)
                        || datum.getGroupid().equals(30)
                        || datum.getGroupid().equals(31)
                        || datum.getGroupid().equals(46)
                        || datum.getGroupid().equals(47)
                        || datum.getGroupid().equals(48)
                        || datum.getGroupid().equals(49)
                        || datum.getGroupid().equals(50)) {
                    if (datum.getCarlicence().length() == 8) {
                        chuZuDianCar++;
                    } else {
                        chuZuYouCar++;
                    }
                    chuZuTherid.add(datum.getTerid());
                    chuZuCount++;
                }
            }
            Map<String, Integer> typeNum = new HashMap<>();
            typeNum.put("2e48d8e7713efe9a519de0fc9628ac1c@" + "0", beiBeiYouCar);
            typeNum.put("2e48d8e7713efe9a519de0fc9628ac1c@" + "1", beiBeiDianCar);
            typeNum.put("db320270101159a4688a17f0c3613a79@" + "0", yuBeiYouCar);
            typeNum.put("db320270101159a4688a17f0c3613a79@" + "1", yuBeiDianCar);
            typeNum.put("4ebcb37f26de278c13ae63d669adb7a1@" + "0", baNanYouCar);
            typeNum.put("4ebcb37f26de278c13ae63d669adb7a1@" + "1", baNanDianCar);
            typeNum.put("883e79b028a7ab36330a9a07e3c6ecf6@" + "0", chuZuYouCar);
            typeNum.put("883e79b028a7ab36330a9a07e3c6ecf6@" + "1", chuZuDianCar);
            //油电车数量
            typeNum.forEach((k, v) -> {
                CarTypeNumEntity carTypeNumEntity = new CarTypeNumEntity();
                String[] split = k.split("@");
                carTypeNumEntity.setType(Integer.parseInt(split[1]));
                carTypeNumEntity.setDeptCode(split[0]);
                carTypeNumEntity.setCarNum(v);
                LambdaQueryWrapper<CarTypeNumEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(CarTypeNumEntity::getDeptCode, carTypeNumEntity.getDeptCode());
                wrapper.eq(CarTypeNumEntity::getType, carTypeNumEntity.getType());
                CarTypeNumEntity carTypeNumEntity1 = carTypeNumMapper.selectOne(wrapper);
                if (!ObjectUtils.isEmpty(carTypeNumEntity1)) {
                    carTypeNumEntity.setId(carTypeNumEntity1.getId());
                    carTypeNumMapper.updateById(carTypeNumEntity);
                } else {
                    carTypeNumMapper.insert(carTypeNumEntity);
                }
            });
            //统计在线设备
            String statusUrl = "http://36.133.68.39:12056/api/v1/basic/state/now";
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("key", jsonObject.getData().getKey());
            for (int i = 0; i < 4; i++) {
                if (i == 0) {
                    paramsMap.put("terid", beiBeiTherid);
                } else if (i == 1) {
                    paramsMap.put("terid", baNanTherid);
                } else if (i == 2) {
                    paramsMap.put("terid", yuBeiTherid);
                } else {
                    paramsMap.put("terid", chuZuTherid);
                }
                String statusResp = PlatHttpUtil.sendPostHttp(statusUrl, JSONObject.toJSONString(paramsMap), null);
                DevicesResp statusData = JSONObject.parseObject(statusResp, DevicesResp.class);
                CarLineEntity carLineEntity = new CarLineEntity();
                carLineEntity.setOnLine(statusData.getData().size());
                carLineEntity.setCreateTime(sdf.format(new Date()) + ":00:00");
                if (i == 0) {
                    carLineEntity.setDeptCode("2e48d8e7713efe9a519de0fc9628ac1c");
                    carLineEntity.setTotalCount(beiBeiCount);
                    System.out.println("北碚在线：" + beiBeiCount + "/" + statusData.getData().size());
                } else if (i == 1) {
                    carLineEntity.setDeptCode("4ebcb37f26de278c13ae63d669adb7a1");
                    carLineEntity.setTotalCount(baNanCount);
                    System.out.println("巴南在线：" + baNanCount + "/" + statusData.getData().size());
                } else if (i == 2) {
                    carLineEntity.setDeptCode("db320270101159a4688a17f0c3613a79");
                    carLineEntity.setTotalCount(yuBeiCount);
                    System.out.println("渝北在线：" + yuBeiCount + "/" + statusData.getData().size());
                } else if (i == 3) {
                    carLineEntity.setDeptCode("883e79b028a7ab36330a9a07e3c6ecf6");
                    carLineEntity.setTotalCount(chuZuCount);
                    System.out.println("出租在线：" + chuZuCount + "/" + statusData.getData().size());
                }
                carLineMapper.insert(carLineEntity);
            }
        } else {
            log.info("获取登录状态失败，");
        }
    }

//    @Scheduled(cron = "0 0 * * * ?")
    public void getSubscribe() {
        String url = "http://36.134.192.18:48081/subscribe";
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("taskid", 4);
        paramsMap.put("subscribeState", true);
        paramsMap.put("callBackUrl", "http://36.139.151.120:9002/biz/data/saveSubscribe");
        list.add(paramsMap);
        String jsonString = JSONObject.toJSONString(list);
        String s = PlatHttpUtil.sendPostHttp(url, jsonString, "1");
        JSONObject jsonObject = JSONObject.parseObject(s);
        String status = jsonObject.get("status").toString();
        if (status.equals("200")) {
            log.info("订阅成功！" + new Date());
        } else {
            log.error("订阅失败！" + new Date());
        }
    }

//    @Scheduled(cron = "0/10 * * * * ?")
    public void getDriverDayNum() {
        SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd");
        String format1 = format.format(new Date());
        for (String deptCode : DATA_CODE) {
            int size = getSize(deptCode)+getSizeNew(deptCode);
            DriverInfoDayNumEntity driverInfoDayNumDto=new DriverInfoDayNumEntity();
            driverInfoDayNumDto.setDeptCode(deptCode);
            driverInfoDayNumDto.setDriverNum(size);
            driverInfoDayNumDto.setDayTime(format1);
            DriverInfoDayNumEntity driverInfoDayNumDto1 = driverInfoDayNumMapper.selectOne(new QueryWrapper<DriverInfoDayNumEntity>()
                    .eq("day_time", format1)
                    .eq("dept_code", deptCode));
            if (!ObjectUtils.isEmpty(driverInfoDayNumDto1)) {
                driverInfoDayNumDto.setDriverNum(size);
                driverInfoDayNumMapper.updateById(driverInfoDayNumDto1);
            }else {
            driverInfoDayNumMapper.insert(driverInfoDayNumDto);
            }
        }
    }

    //人员数据
    private int getSize(String deptCode) {
        Criteria criteria = new Criteria();
        //过滤条件
        if (deptCode.equals(DATA_CODE[2])) {
            List<String> stream = Arrays.asList(CZC_SON_CODE);
            criteria.and("suoShuGongSiJiCheDui").in(stream);
        } else if (deptCode.equals(DATA_CODE[1])) {
            List<String> stream = Arrays.asList(BB_SON_CODE);
            criteria.and("suoShuGongSiJiCheDui").in(stream);
        } else {
            criteria.and("suoShuGongSiJiCheDui").in(deptCode);
        }
        criteria.and("zhuangTai").is("在职");
        //筛选时间范围
        TypedAggregation<DriverInfoDto> aggregation = Aggregation.newAggregation(DriverInfoDto.class,
                Aggregation.match(criteria)
        );
        List<DriverInfoDto> mappedResults = mongoTemplate.aggregate(aggregation, DriverInfoDto.class).getMappedResults();
        return mappedResults.size();
    }
    //草稿数据
    private int getSizeNew(String deptCode) {
        Criteria criteria = new Criteria();
        //过滤条件
        if (deptCode.equals(DATA_CODE[2])) {
            List<String> stream = Arrays.asList(CZC_SON_CODE);
            criteria.and("suoShuFenGongSiJiCheDui").in(stream);
        } else if (deptCode.equals(DATA_CODE[1])) {
            List<String> stream = Arrays.asList(BB_SON_CODE);
            criteria.and("suoShuFenGongSiJiCheDui").in(stream);
        } else {
            criteria.and("suoShuFenGongSiJiCheDui").in(deptCode);
        }
        criteria.and("jvsFlowTaskProgress").is("草稿");
        //筛选时间范围
        TypedAggregation<DriverQuaRreviewDto> aggregation = Aggregation.newAggregation(DriverQuaRreviewDto.class,
                Aggregation.match(criteria)
        );
        List<DriverQuaRreviewDto> mappedResults = mongoTemplate.aggregate(aggregation, DriverQuaRreviewDto.class).getMappedResults();
        return mappedResults.size();
    }
}
