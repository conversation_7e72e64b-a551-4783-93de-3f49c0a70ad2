
package com.kan.modules.sys.utils;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cglib.beans.BeanMap;

public class BeanCopyUtil {
    private static final Logger log = LoggerFactory.getLogger(BeanCopyUtil.class);

    public BeanCopyUtil() {
    }

    public static <T> List<T> copys(List source, Class<T> cls) {
        return (List)source.stream().map((e) -> copy(e, cls)).collect(Collectors.toList());
    }

    public static <T> T copy(Object source, Class<T> cls) {
        if (source instanceof Map) {
            return (T)JSON.to(cls, source);
        } else {
            return (T)(source instanceof String ? JSON.to(cls, source) : JSON.to(cls, JSON.toJSONString(source)));
        }
    }

    public static <T> T copy(Class<T> cls, Object... source) {
        if (source.length == 1) {
            return (T)copy(source[0], cls);
        } else {
            T o = null;

            try {
                o = (T)cls.newInstance();

                for(Object o1 : source) {
                    BeanUtil.copyProperties(o1, o, new String[0]);
                }

                return o;
            } catch (InstantiationException e) {
                log.info("转化错误", e);
            } catch (IllegalAccessException e) {
                log.info("转化错误", e);
            }

            return o;
        }
    }

    public static <T> Map<String, Object> beanToMap(T bean) {
        Map<String, Object> map = new HashMap(5);
        if (bean != null) {
            BeanMap beanMap = BeanMap.create(bean);

            for(Object key : beanMap.keySet()) {
                Object value = beanMap.get(key);
                if (value != null) {
                    map.put(key + "", value);
                }
            }
        }

        return map;
    }

    public static <T> List<Map<String, Object>> objectsToMaps(List<T> objList) {
        List<Map<String, Object>> list = new ArrayList(5);
        if (objList != null && objList.size() > 0) {
            Map<String, Object> map = null;
            T bean = null;
            int i = 0;

            for(int size = objList.size(); i < size; ++i) {
                bean = (T)objList.get(i);
                map = beanToMap(bean);
                list.add(map);
            }
        }

        return list;
    }

    public static <T> List<T> deepCopy(List<T> t) {
        try {
            ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
            ObjectOutputStream out = new ObjectOutputStream(byteOut);
            out.writeObject(t);
            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
            ObjectInputStream in = new ObjectInputStream(byteIn);
            return (List)in.readObject();
        } catch (ClassNotFoundException | IOException e) {
            log.info("deepCopy failure!! error={}", ((Exception)e).getMessage());
            return null;
        }
    }

    public static <T> T deepCopy(T t) {
        if (t instanceof List) {
            return (T)deepCopy((List)t);
        } else {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ObjectOutputStream oos = new ObjectOutputStream(baos);
                oos.writeObject(t);
                ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
                ObjectInputStream ois = new ObjectInputStream(bais);
                return (T)ois.readObject();
            } catch (ClassNotFoundException | IOException e) {
                log.info("deepCopy failure!! error={}", ((Exception)e).getMessage());
                return null;
            }
        }
    }
}
