package com.kan.modules.sys.utils;

import java.util.List;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(
    prefix = "oss"
)
@Component
public class OssProperties {
    private String endpoint;
    private String accessKey;
    private String secretKey;
    private List<String> publicBuckets;
    private Integer timelinessHour = 168;

    public OssProperties() {
    }

    public String getEndpoint() {
        return this.endpoint;
    }

    public String getAccessKey() {
        return this.accessKey;
    }

    public String getSecretKey() {
        return this.secretKey;
    }

    public List<String> getPublicBuckets() {
        return this.publicBuckets;
    }

    public Integer getTimelinessHour() {
        return this.timelinessHour;
    }

    public OssProperties setEndpoint(final String endpoint) {
        this.endpoint = endpoint;
        return this;
    }

    public OssProperties setAccessKey(final String accessKey) {
        this.accessKey = accessKey;
        return this;
    }

    public OssProperties setSecretKey(final String secretKey) {
        this.secretKey = secretKey;
        return this;
    }

    public OssProperties setPublicBuckets(final List<String> publicBuckets) {
        this.publicBuckets = publicBuckets;
        return this;
    }

    public OssProperties setTimelinessHour(final Integer timelinessHour) {
        this.timelinessHour = timelinessHour;
        return this;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof OssProperties)) {
            return false;
        } else {
            OssProperties other = (OssProperties)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$timelinessHour = this.getTimelinessHour();
                Object other$timelinessHour = other.getTimelinessHour();
                if (this$timelinessHour == null) {
                    if (other$timelinessHour != null) {
                        return false;
                    }
                } else if (!this$timelinessHour.equals(other$timelinessHour)) {
                    return false;
                }

                Object this$endpoint = this.getEndpoint();
                Object other$endpoint = other.getEndpoint();
                if (this$endpoint == null) {
                    if (other$endpoint != null) {
                        return false;
                    }
                } else if (!this$endpoint.equals(other$endpoint)) {
                    return false;
                }

                Object this$accessKey = this.getAccessKey();
                Object other$accessKey = other.getAccessKey();
                if (this$accessKey == null) {
                    if (other$accessKey != null) {
                        return false;
                    }
                } else if (!this$accessKey.equals(other$accessKey)) {
                    return false;
                }

                Object this$secretKey = this.getSecretKey();
                Object other$secretKey = other.getSecretKey();
                if (this$secretKey == null) {
                    if (other$secretKey != null) {
                        return false;
                    }
                } else if (!this$secretKey.equals(other$secretKey)) {
                    return false;
                }

                Object this$publicBuckets = this.getPublicBuckets();
                Object other$publicBuckets = other.getPublicBuckets();
                if (this$publicBuckets == null) {
                    if (other$publicBuckets != null) {
                        return false;
                    }
                } else if (!this$publicBuckets.equals(other$publicBuckets)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof OssProperties;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $timelinessHour = this.getTimelinessHour();
        result = result * 59 + ($timelinessHour == null ? 43 : $timelinessHour.hashCode());
        Object $endpoint = this.getEndpoint();
        result = result * 59 + ($endpoint == null ? 43 : $endpoint.hashCode());
        Object $accessKey = this.getAccessKey();
        result = result * 59 + ($accessKey == null ? 43 : $accessKey.hashCode());
        Object $secretKey = this.getSecretKey();
        result = result * 59 + ($secretKey == null ? 43 : $secretKey.hashCode());
        Object $publicBuckets = this.getPublicBuckets();
        result = result * 59 + ($publicBuckets == null ? 43 : $publicBuckets.hashCode());
        return result;
    }

    public String toString() {
        return "OssProperties(endpoint=" + this.getEndpoint() + ", accessKey=" + this.getAccessKey() + ", secretKey=" + this.getSecretKey() + ", publicBuckets=" + this.getPublicBuckets() + ", timelinessHour=" + this.getTimelinessHour() + ")";
    }
}
