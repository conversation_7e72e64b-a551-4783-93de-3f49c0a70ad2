package com.kan.modules.sys.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;

/**
 * 邮件配置检查工具
 * 应用启动时检查邮件配置是否正确
 */
@Slf4j
@Component
public class EmailConfigChecker implements CommandLineRunner {

    @Autowired(required = false)
    private JavaMailSender mailSender;

    @Value("${spring.mail.host:}")
    private String mailHost;

    @Value("${spring.mail.port:0}")
    private int mailPort;

    @Value("${spring.mail.username:}")
    private String mailUsername;

    @Value("${spring.mail.from:}")
    private String mailFrom;

    @Override
    public void run(String... args) throws Exception {
        checkEmailConfiguration();
    }

    private void checkEmailConfiguration() {
        log.info("=== 邮件配置检查 ===");
        
        if (mailSender == null) {
            log.error("❌ JavaMailSender未配置，请检查spring-boot-starter-mail依赖");
            return;
        }

        if (mailHost.isEmpty()) {
            log.error("❌ 邮件服务器地址未配置 (spring.mail.host)");
            return;
        }

        if (mailPort == 0) {
            log.error("❌ 邮件服务器端口未配置 (spring.mail.port)");
            return;
        }

        if (mailUsername.isEmpty()) {
            log.error("❌ 邮件用户名未配置 (spring.mail.username)");
            return;
        }

        if (mailFrom.isEmpty()) {
            log.error("❌ 发送邮箱地址未配置 (spring.mail.from)");
            return;
        }

        // 所有配置都正确
        log.info("✅ 邮件配置检查通过");
        log.info("📧 SMTP服务器: {}:{}", mailHost, mailPort);
        log.info("👤 发送邮箱: {}", mailFrom);
        log.info("📝 可以使用以下接口测试邮件功能:");
        log.info("   - POST /sys/email/test/sendSimple");
        log.info("   - POST /sys/email/test/sendHtml");
        log.info("   - POST /sys/email/test/sendToAdmin");
        log.info("   - POST /sys/email/test/testWithdrawNotification");
        log.info("===================");
    }
}
