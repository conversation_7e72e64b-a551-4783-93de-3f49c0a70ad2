//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.kan.modules.sys.utils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

@ApiModel("文件信息")
public class FileNameDto implements Serializable {
    private static final long serialVersionUID = 5216665175529677971L;
    @ApiModelProperty("文件名称")
    String fileName;
    @ApiModelProperty("原文件名称")
    String originalFileName;
    @ApiModelProperty("桶名称")
    String bucketName;
    @ApiModelProperty("时间")
    Date date;
    @ApiModelProperty("开始上传时间")
    Date startTime;
    @ApiModelProperty("截至上传时间")
    Date endTime;
    @ApiModelProperty("文件大小")
    Long fileSize;
    @ApiModelProperty("文件外链")
    String fileLink;

    public FileNameDto() {
    }

    public String getFileName() {
        return this.fileName;
    }

    public String getOriginalFileName() {
        return this.originalFileName;
    }

    public String getBucketName() {
        return this.bucketName;
    }

    public Date getDate() {
        return this.date;
    }

    public Date getStartTime() {
        return this.startTime;
    }

    public Date getEndTime() {
        return this.endTime;
    }

    public Long getFileSize() {
        return this.fileSize;
    }

    public String getFileLink() {
        return this.fileLink;
    }

    public FileNameDto setFileName(final String fileName) {
        this.fileName = fileName;
        return this;
    }

    public FileNameDto setOriginalFileName(final String originalFileName) {
        this.originalFileName = originalFileName;
        return this;
    }

    public FileNameDto setBucketName(final String bucketName) {
        this.bucketName = bucketName;
        return this;
    }

    public FileNameDto setDate(final Date date) {
        this.date = date;
        return this;
    }

    public FileNameDto setStartTime(final Date startTime) {
        this.startTime = startTime;
        return this;
    }

    public FileNameDto setEndTime(final Date endTime) {
        this.endTime = endTime;
        return this;
    }

    public FileNameDto setFileSize(final Long fileSize) {
        this.fileSize = fileSize;
        return this;
    }

    public FileNameDto setFileLink(final String fileLink) {
        this.fileLink = fileLink;
        return this;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof FileNameDto)) {
            return false;
        } else {
            FileNameDto other = (FileNameDto)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$fileSize = this.getFileSize();
                Object other$fileSize = other.getFileSize();
                if (this$fileSize == null) {
                    if (other$fileSize != null) {
                        return false;
                    }
                } else if (!this$fileSize.equals(other$fileSize)) {
                    return false;
                }

                Object this$fileName = this.getFileName();
                Object other$fileName = other.getFileName();
                if (this$fileName == null) {
                    if (other$fileName != null) {
                        return false;
                    }
                } else if (!this$fileName.equals(other$fileName)) {
                    return false;
                }

                Object this$originalFileName = this.getOriginalFileName();
                Object other$originalFileName = other.getOriginalFileName();
                if (this$originalFileName == null) {
                    if (other$originalFileName != null) {
                        return false;
                    }
                } else if (!this$originalFileName.equals(other$originalFileName)) {
                    return false;
                }

                Object this$bucketName = this.getBucketName();
                Object other$bucketName = other.getBucketName();
                if (this$bucketName == null) {
                    if (other$bucketName != null) {
                        return false;
                    }
                } else if (!this$bucketName.equals(other$bucketName)) {
                    return false;
                }

                Object this$date = this.getDate();
                Object other$date = other.getDate();
                if (this$date == null) {
                    if (other$date != null) {
                        return false;
                    }
                } else if (!this$date.equals(other$date)) {
                    return false;
                }

                Object this$startTime = this.getStartTime();
                Object other$startTime = other.getStartTime();
                if (this$startTime == null) {
                    if (other$startTime != null) {
                        return false;
                    }
                } else if (!this$startTime.equals(other$startTime)) {
                    return false;
                }

                Object this$endTime = this.getEndTime();
                Object other$endTime = other.getEndTime();
                if (this$endTime == null) {
                    if (other$endTime != null) {
                        return false;
                    }
                } else if (!this$endTime.equals(other$endTime)) {
                    return false;
                }

                Object this$fileLink = this.getFileLink();
                Object other$fileLink = other.getFileLink();
                if (this$fileLink == null) {
                    if (other$fileLink != null) {
                        return false;
                    }
                } else if (!this$fileLink.equals(other$fileLink)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof FileNameDto;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $fileSize = this.getFileSize();
        result = result * 59 + ($fileSize == null ? 43 : $fileSize.hashCode());
        Object $fileName = this.getFileName();
        result = result * 59 + ($fileName == null ? 43 : $fileName.hashCode());
        Object $originalFileName = this.getOriginalFileName();
        result = result * 59 + ($originalFileName == null ? 43 : $originalFileName.hashCode());
        Object $bucketName = this.getBucketName();
        result = result * 59 + ($bucketName == null ? 43 : $bucketName.hashCode());
        Object $date = this.getDate();
        result = result * 59 + ($date == null ? 43 : $date.hashCode());
        Object $startTime = this.getStartTime();
        result = result * 59 + ($startTime == null ? 43 : $startTime.hashCode());
        Object $endTime = this.getEndTime();
        result = result * 59 + ($endTime == null ? 43 : $endTime.hashCode());
        Object $fileLink = this.getFileLink();
        result = result * 59 + ($fileLink == null ? 43 : $fileLink.hashCode());
        return result;
    }

    public String toString() {
        return "FileNameDto(fileName=" + this.getFileName() + ", originalFileName=" + this.getOriginalFileName() + ", bucketName=" + this.getBucketName() + ", date=" + this.getDate() + ", startTime=" + this.getStartTime() + ", endTime=" + this.getEndTime() + ", fileSize=" + this.getFileSize() + ", fileLink=" + this.getFileLink() + ")";
    }
}
