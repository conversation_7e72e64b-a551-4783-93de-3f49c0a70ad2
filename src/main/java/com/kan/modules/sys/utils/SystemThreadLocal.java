//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.kan.modules.sys.utils;

import com.alibaba.ttl.TransmittableThreadLocal;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SystemThreadLocal<T> {
    private static final Logger log = LoggerFactory.getLogger(SystemThreadLocal.class);
    private static ThreadLocal<Map<String, Object>> local = new TransmittableThreadLocal();

    public SystemThreadLocal() {
    }

    public static <T> void set(String k, T v) {
        Map<String, Object> map = (Map)local.get();
        if (map == null) {
            Map<String, Object> var3 = new HashMap(80);
            local.set(var3);
        }

        ((Map)local.get()).put(k, v);
    }

    public static <T> T get(String key) {
        Map<String, Object> stringObjectMap = (Map)local.get();
        if (stringObjectMap == null) {
            HashMap<String, Object> map = new HashMap(80);
            local.set(map);
        }

        return (T)((Map)local.get()).get(key);
    }

    public static Map<String, Object> get() {
        return (Map)local.get();
    }

    public static void setAll(Map<String, Object> map) {
        local.set(map);
    }

    public static void remove(String key) {
        ((Map)local.get()).remove(key);
    }

    public static void clear() {
        local.remove();
    }
}
