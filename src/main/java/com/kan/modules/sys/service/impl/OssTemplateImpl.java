package com.kan.modules.sys.service.impl;

import cn.hutool.core.date.DateUtil;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.util.IOUtils;
import com.kan.modules.sys.form.BaseFile;
import com.kan.modules.sys.service.OssTemplate;
import com.kan.modules.sys.utils.IdGenerator;
import com.kan.modules.sys.utils.MimeMappings;
import com.kan.modules.sys.utils.OssProperties;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * OSS模板实现类
 *
 * <AUTHOR>
 */
@Service
public class OssTemplateImpl implements OssTemplate {

    @Resource
    private OssProperties ossProperties;
    @Resource
    private AmazonS3Client s3;

    String STR = "{\n    \"Version\":\"2012-10-17\",\n    \"Statement\":[\n        {\n            \"Effect\":\"Allow\",\n            \"Principal\":{\n                \"AWS\":[\n                    \"*\"\n                ]\n            },\n            \"Action\":[\n                \"s3:GetBucketLocation\",\n                \"s3:ListBucket\",\n                \"s3:ListBucketMultipartUploads\"\n            ],\n            \"Resource\":[\n                \"arn:aws:s3:::%s\"\n            ]\n        },\n        {\n            \"Effect\":\"Allow\",\n            \"Principal\":{\n                \"AWS\":[\n                    \"*\"\n                ]\n            },\n            \"Action\":[\n                \"s3:DeleteObject\",\n                \"s3:GetObject\",\n                \"s3:ListMultipartUploadParts\",\n                \"s3:PutObject\",\n                \"s3:AbortMultipartUpload\"\n            ],\n            \"Resource\":[\n                \"arn:aws:s3:::%s/*\"\n            ]\n        }\n    ]\n}";
    Map<String, String> MIME_MAPPINGS = new HashMap<String, String>(101) {
        {
            this.putAll(MimeMappings.DEFAULT_MIME_MAPPINGS);
            this.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            this.put("doc", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        }
    };

    @Override
    public void makeBucket(String bucketName) {
        if (!this.bucketExists(bucketName)) {
            this.s3.createBucket(bucketName);
        }
    }

    @Override
    public boolean bucketExists(String bucketName) {
        return this.s3.listBuckets().stream().filter((e) -> e.getName().contains(bucketName)).findFirst().isPresent();
    }

    public static String getFileName(String module, String originalFilename) {
        String today = DateUtil.format(new Date(), "yyyy/MM/dd/");
        return today + DateUtil.today() + IdGenerator.getId() + "-" + originalFilename;
    }

    @Override
    public String fileLink(String fileName, String bucketName) {
        if (this.ossProperties.getPublicBuckets().contains(bucketName)) {
            return this.s3.getResourceUrl(bucketName, fileName);
        } else {
            long time = System.currentTimeMillis() + Long.valueOf((long) (this.ossProperties.getTimelinessHour() * 3600 * 1000));
            return this.s3.generatePresignedUrl(bucketName, fileName, new Date(time)).toString();
        }
    }

    @Override
    public BaseFile putFile(String bucketName, String module, String originalName, MultipartFile file) throws Exception {
        try {
            String str = ".";
            if (originalName.indexOf(str) == -1 && MIME_MAPPINGS.containsValue(file.getContentType())) {
                String value = (String) MIME_MAPPINGS.keySet().stream().filter((e) -> ((String) MIME_MAPPINGS.get(e)).equals(file.getContentType())).findFirst().get();
                originalName = originalName + str + value;
            }

            return this.putFileTwo(bucketName, module, originalName, file.getInputStream());
        } catch (Throwable $ex) {
            throw $ex;
        }
    }

    @Override
    public BaseFile putFileTwo(String bucketName, String module, String originalName, InputStream inputStream) {
        return this.put(bucketName, module, inputStream, originalName, false);
    }

    @Override
    public BaseFile put(String bucketName, String module, InputStream stream, String key, boolean cover) {
        try {
            ObjectMetadata metadata = new ObjectMetadata();
            Long size = (long) stream.available();
            this.makeBucket(bucketName);
            String originalName = key;
            key = this.getFileName(module, key);
            int i = key.lastIndexOf(".") + 1;
            String substring = key.substring(i);
            String contentType = (String) MIME_MAPPINGS.get(substring);
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            String s3Key = key.replaceAll("//", "/");
            metadata.setContentType(contentType);
            this.s3.putObject(bucketName, s3Key, new ByteArrayInputStream(IOUtils.toByteArray(stream)), metadata);
            BaseFile file = new BaseFile();
            file.setOriginalName(originalName);
            file.setFileName(s3Key);
            file.setBucketName(bucketName);
            file.setFileType(contentType);
            file.setModule(module);
            file.setSize(size);
            return file;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    ;
}
