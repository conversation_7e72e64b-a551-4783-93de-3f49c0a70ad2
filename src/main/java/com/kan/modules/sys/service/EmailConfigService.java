package com.kan.modules.sys.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Service;

import java.util.Properties;

/**
 * 邮件配置服务
 * 用于动态切换邮件配置
 */
@Slf4j
@Service
public class EmailConfigService {

    @Autowired
    private JavaMailSender mailSender;

    /**
     * 配置163邮箱SSL模式
     */
    public void configure163SSL() {
        if (mailSender instanceof JavaMailSenderImpl) {
            JavaMailSenderImpl sender = (JavaMailSenderImpl) mailSender;
            
            sender.setHost("smtp.163.com");
            sender.setPort(465);
            sender.setUsername("<EMAIL>");
            sender.setPassword("UEdAU3BygTGzf8mx");
            
            Properties props = new Properties();
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.ssl.enable", "true");
            props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
            props.put("mail.smtp.socketFactory.port", "465");
            props.put("mail.smtp.connectiontimeout", "30000");
            props.put("mail.smtp.timeout", "30000");
            props.put("mail.smtp.writetimeout", "30000");
            
            sender.setJavaMailProperties(props);
            log.info("已切换到163邮箱SSL模式 (端口465)");
        }
    }

    /**
     * 配置163邮箱STARTTLS模式
     */
    public void configure163STARTTLS() {
        if (mailSender instanceof JavaMailSenderImpl) {
            JavaMailSenderImpl sender = (JavaMailSenderImpl) mailSender;
            
            sender.setHost("smtp.163.com");
            sender.setPort(587);
            sender.setUsername("<EMAIL>");
            sender.setPassword("UEdAU3BygTGzf8mx");
            
            Properties props = new Properties();
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
            props.put("mail.smtp.connectiontimeout", "30000");
            props.put("mail.smtp.timeout", "30000");
            props.put("mail.smtp.writetimeout", "30000");
            
            sender.setJavaMailProperties(props);
            log.info("已切换到163邮箱STARTTLS模式 (端口587)");
        }
    }

    /**
     * 配置163邮箱无加密模式（仅测试用）
     */
    public void configure163Plain() {
        if (mailSender instanceof JavaMailSenderImpl) {
            JavaMailSenderImpl sender = (JavaMailSenderImpl) mailSender;
            
            sender.setHost("smtp.163.com");
            sender.setPort(25);
            sender.setUsername("<EMAIL>");
            sender.setPassword("UEdAU3BygTGzf8mx");
            
            Properties props = new Properties();
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.connectiontimeout", "30000");
            props.put("mail.smtp.timeout", "30000");
            props.put("mail.smtp.writetimeout", "30000");
            
            sender.setJavaMailProperties(props);
            log.info("已切换到163邮箱无加密模式 (端口25)");
        }
    }

    /**
     * 获取当前邮件配置信息
     */
    public String getCurrentConfig() {
        if (mailSender instanceof JavaMailSenderImpl) {
            JavaMailSenderImpl sender = (JavaMailSenderImpl) mailSender;
            return String.format("当前配置: %s:%d, 用户: %s", 
                sender.getHost(), sender.getPort(), sender.getUsername());
        }
        return "无法获取当前配置";
    }
}
