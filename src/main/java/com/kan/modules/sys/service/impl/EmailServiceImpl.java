package com.kan.modules.sys.service.impl;

import com.kan.common.exception.KanException;
import com.kan.common.utils.Constant;
import com.kan.modules.sys.entity.SysUserEntity;
import com.kan.modules.sys.service.EmailService;
import com.kan.modules.sys.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.internet.MimeMessage;
import java.util.List;

/**
 * 邮件服务实现类
 * 已配置163邮箱SMTP服务器，支持实际邮件发送
 */
@Slf4j
@Service
public class EmailServiceImpl implements EmailService {

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private SysUserService sysUserService;

    @Value("${spring.mail.from}")
    private String fromEmail;

    @Override
    public void sendSimpleEmail(String to, String subject, String content) {
        try {
            log.info("开始发送邮件 - 收件人: {}, 主题: {}", to, subject);

            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(to);
            message.setSubject(subject);
            message.setText(content);

            mailSender.send(message);
            log.info("邮件发送成功 - 收件人: {}", to);

        } catch (Exception e) {
            log.error("发送邮件失败 - 收件人: {}, 错误: {}", to, e.getMessage(), e);
            throw new KanException("发送邮件失败: " + e.getMessage());
        }
    }

    @Override
    public void sendHtmlEmail(String to, String subject, String content) {
        try {
            log.info("开始发送HTML邮件 - 收件人: {}, 主题: {}", to, subject);

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setFrom(fromEmail);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);

            mailSender.send(message);
            log.info("HTML邮件发送成功 - 收件人: {}", to);

        } catch (Exception e) {
            log.error("发送HTML邮件失败 - 收件人: {}, 错误: {}", to, e.getMessage(), e);
            throw new KanException("发送HTML邮件失败: " + e.getMessage());
        }
    }

    @Override
    public void sendEmailToAdmin(String subject, String content) {
        try {
            // 获取超级管理员信息
            SysUserEntity adminUser = sysUserService.getById((long) Constant.SUPER_ADMIN);
            if (adminUser != null && adminUser.getEmail() != null) {
                sendSimpleEmail(adminUser.getEmail(), subject, content);
                log.info("已发送邮件给管理员: {}", adminUser.getEmail());
            } else {
                // 如果没有找到管理员邮箱，发送到默认邮箱
                String defaultAdminEmail = "<EMAIL>"; // 使用配置的发送邮箱作为默认管理员邮箱
                sendSimpleEmail(defaultAdminEmail, subject, content);
                log.info("未找到管理员邮箱，已发送到默认管理员邮箱: {}", defaultAdminEmail);
            }
        } catch (Exception e) {
            log.error("发送邮件给管理员失败", e);
            throw new KanException("发送邮件给管理员失败: " + e.getMessage());
        }
    }
}
