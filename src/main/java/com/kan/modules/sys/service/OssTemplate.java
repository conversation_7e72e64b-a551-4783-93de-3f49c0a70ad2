
package com.kan.modules.sys.service;

import java.io.InputStream;

import com.kan.modules.sys.form.BaseFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public interface OssTemplate {

    BaseFile putFile(String bucketName, String module, String originalName, MultipartFile file) throws Exception;

    BaseFile putFileTwo(String bucketName, String module, String originalName, InputStream inputStream);

    BaseFile put(String bucketName, String module, InputStream stream, String originalName, boolean cover);

    String fileLink(String fileName, String bucketName);
}
