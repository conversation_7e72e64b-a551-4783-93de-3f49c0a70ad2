//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON>rn<PERSON>lower decompiler)
//

package com.kan.modules.sys.service;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import com.kan.modules.sys.form.BaseFile;
import com.kan.modules.sys.utils.MimeMappings;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public interface OssTemplate {
    String STR = "{\n    \"Version\":\"2012-10-17\",\n    \"Statement\":[\n        {\n            \"Effect\":\"Allow\",\n            \"Principal\":{\n                \"AWS\":[\n                    \"*\"\n                ]\n            },\n            \"Action\":[\n                \"s3:GetBucketLocation\",\n                \"s3:ListBucket\",\n                \"s3:ListBucketMultipartUploads\"\n            ],\n            \"Resource\":[\n                \"arn:aws:s3:::%s\"\n            ]\n        },\n        {\n            \"Effect\":\"Allow\",\n            \"Principal\":{\n                \"AWS\":[\n                    \"*\"\n                ]\n            },\n            \"Action\":[\n                \"s3:DeleteObject\",\n                \"s3:GetObject\",\n                \"s3:ListMultipartUploadParts\",\n                \"s3:PutObject\",\n                \"s3:AbortMultipartUpload\"\n            ],\n            \"Resource\":[\n                \"arn:aws:s3:::%s/*\"\n            ]\n        }\n    ]\n}";
    Map<String, String> MIME_MAPPINGS = new HashMap<String, String>(101) {
        {
            this.putAll(MimeMappings.DEFAULT_MIME_MAPPINGS);
            this.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            this.put("doc", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        }
    };

    void makeBucket(String bucketName);

    boolean bucketExists(String bucketName);

    void listObjects(String bucketName, String path, String label);

    String fileLink(String fileName, String bucketName);

    default BaseFile putFile(String bucketName, String module, String originalName, MultipartFile file) throws Exception{
        try {
            String str = ".";
            if (originalName.indexOf(str) == -1 && MIME_MAPPINGS.containsValue(file.getContentType())) {
                String value = (String)MIME_MAPPINGS.keySet().stream().filter((e) -> ((String)MIME_MAPPINGS.get(e)).equals(file.getContentType())).findFirst().get();
                originalName = originalName + str + value;
            }

            return this.putFile(bucketName, module, originalName, file.getInputStream());
        } catch (Throwable $ex) {
            throw $ex;
        }
    }

    default BaseFile putFile(String bucketName, String module, String originalName, InputStream inputStream) {
        return this.put(bucketName, module, inputStream, originalName, false);
    }

    BaseFile put(String bucketName, String module, InputStream stream, String originalName, boolean cover);

}
