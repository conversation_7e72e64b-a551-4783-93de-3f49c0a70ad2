
package com.kan.modules.sys.form;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Locale;
import org.springframework.format.annotation.DateTimeFormat;

@TableName("sys_file")
public class OssFile implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(
        value = "id",
        type = IdType.ASSIGN_UUID
    )
    private String id;
    private String fileName;
    private String bucketName;
    @TableField("module_name")
    private String module;
    private String filePath;
    @DateTimeFormat(
        pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
        pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private LocalDateTime createTime;
    @TableField("file_size")
    private Long size;
    @TableField(
        exist = false
    )
    private String fileSize;
    private String fileType;
    private String fileLink;
    private String label;
    private String tenantId;
    @TableField(
        exist = false
    )
    private String thumbnail;

    public String getFileSize() {
        return this.size == null ? "" : byteFormat(this.size, true);
    }

    public static String byteFormat(long bytes, boolean needUnit) {
        String[] units = new String[]{" B", " KB", " MB", " GB", " TB", " PB", " EB", " ZB", " YB"};
        if (bytes <= 0L) {
            return "0.0 B";
        } else {
            int unit = 1024;
            int exp = (int)(Math.log((double)bytes) / Math.log((double)unit));
            double pre = (double)0.0F;
            if (bytes > (long)unit) {
                pre = (double)bytes / Math.pow((double)unit, (double)exp);
            } else {
                pre = (double)bytes / (double)unit;
            }

            return needUnit ? String.format(Locale.ENGLISH, "%.1f%s", pre, units[exp]) : String.format(Locale.ENGLISH, "%.1f", pre);
        }
    }

    public String getId() {
        return this.id;
    }

    public String getFileName() {
        return this.fileName;
    }

    public String getBucketName() {
        return this.bucketName;
    }

    public String getModule() {
        return this.module;
    }

    public String getFilePath() {
        return this.filePath;
    }

    public LocalDateTime getCreateTime() {
        return this.createTime;
    }

    public Long getSize() {
        return this.size;
    }

    public String getFileType() {
        return this.fileType;
    }

    public String getFileLink() {
        return this.fileLink;
    }

    public String getLabel() {
        return this.label;
    }

    public String getTenantId() {
        return this.tenantId;
    }

    public String getThumbnail() {
        return this.thumbnail;
    }

    public OssFile setId(final String id) {
        this.id = id;
        return this;
    }

    public OssFile setFileName(final String fileName) {
        this.fileName = fileName;
        return this;
    }

    public OssFile setBucketName(final String bucketName) {
        this.bucketName = bucketName;
        return this;
    }

    public OssFile setModule(final String module) {
        this.module = module;
        return this;
    }

    public OssFile setFilePath(final String filePath) {
        this.filePath = filePath;
        return this;
    }

    @JsonFormat(
        pattern = "yyyy-MM-dd HH:mm:ss"
    )
    public OssFile setCreateTime(final LocalDateTime createTime) {
        this.createTime = createTime;
        return this;
    }

    public OssFile setSize(final Long size) {
        this.size = size;
        return this;
    }

    public OssFile setFileSize(final String fileSize) {
        this.fileSize = fileSize;
        return this;
    }

    public OssFile setFileType(final String fileType) {
        this.fileType = fileType;
        return this;
    }

    public OssFile setFileLink(final String fileLink) {
        this.fileLink = fileLink;
        return this;
    }

    public OssFile setLabel(final String label) {
        this.label = label;
        return this;
    }

    public OssFile setTenantId(final String tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public OssFile setThumbnail(final String thumbnail) {
        this.thumbnail = thumbnail;
        return this;
    }

    public String toString() {
        return "OssFile(id=" + this.getId() + ", fileName=" + this.getFileName() + ", bucketName=" + this.getBucketName() + ", module=" + this.getModule() + ", filePath=" + this.getFilePath() + ", createTime=" + this.getCreateTime() + ", size=" + this.getSize() + ", fileSize=" + this.getFileSize() + ", fileType=" + this.getFileType() + ", fileLink=" + this.getFileLink() + ", label=" + this.getLabel() + ", tenantId=" + this.getTenantId() + ", thumbnail=" + this.getThumbnail() + ")";
    }

    public OssFile(final String id, final String fileName, final String bucketName, final String module, final String filePath, final LocalDateTime createTime, final Long size, final String fileSize, final String fileType, final String fileLink, final String label, final String tenantId, final String thumbnail) {
        this.id = id;
        this.fileName = fileName;
        this.bucketName = bucketName;
        this.module = module;
        this.filePath = filePath;
        this.createTime = createTime;
        this.size = size;
        this.fileSize = fileSize;
        this.fileType = fileType;
        this.fileLink = fileLink;
        this.label = label;
        this.tenantId = tenantId;
        this.thumbnail = thumbnail;
    }

    public OssFile() {
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof OssFile)) {
            return false;
        } else {
            OssFile other = (OssFile)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$size = this.getSize();
                Object other$size = other.getSize();
                if (this$size == null) {
                    if (other$size != null) {
                        return false;
                    }
                } else if (!this$size.equals(other$size)) {
                    return false;
                }

                Object this$id = this.getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }

                Object this$fileName = this.getFileName();
                Object other$fileName = other.getFileName();
                if (this$fileName == null) {
                    if (other$fileName != null) {
                        return false;
                    }
                } else if (!this$fileName.equals(other$fileName)) {
                    return false;
                }

                Object this$bucketName = this.getBucketName();
                Object other$bucketName = other.getBucketName();
                if (this$bucketName == null) {
                    if (other$bucketName != null) {
                        return false;
                    }
                } else if (!this$bucketName.equals(other$bucketName)) {
                    return false;
                }

                Object this$module = this.getModule();
                Object other$module = other.getModule();
                if (this$module == null) {
                    if (other$module != null) {
                        return false;
                    }
                } else if (!this$module.equals(other$module)) {
                    return false;
                }

                Object this$filePath = this.getFilePath();
                Object other$filePath = other.getFilePath();
                if (this$filePath == null) {
                    if (other$filePath != null) {
                        return false;
                    }
                } else if (!this$filePath.equals(other$filePath)) {
                    return false;
                }

                Object this$createTime = this.getCreateTime();
                Object other$createTime = other.getCreateTime();
                if (this$createTime == null) {
                    if (other$createTime != null) {
                        return false;
                    }
                } else if (!this$createTime.equals(other$createTime)) {
                    return false;
                }

                Object this$fileSize = this.getFileSize();
                Object other$fileSize = other.getFileSize();
                if (this$fileSize == null) {
                    if (other$fileSize != null) {
                        return false;
                    }
                } else if (!this$fileSize.equals(other$fileSize)) {
                    return false;
                }

                Object this$fileType = this.getFileType();
                Object other$fileType = other.getFileType();
                if (this$fileType == null) {
                    if (other$fileType != null) {
                        return false;
                    }
                } else if (!this$fileType.equals(other$fileType)) {
                    return false;
                }

                Object this$fileLink = this.getFileLink();
                Object other$fileLink = other.getFileLink();
                if (this$fileLink == null) {
                    if (other$fileLink != null) {
                        return false;
                    }
                } else if (!this$fileLink.equals(other$fileLink)) {
                    return false;
                }

                Object this$label = this.getLabel();
                Object other$label = other.getLabel();
                if (this$label == null) {
                    if (other$label != null) {
                        return false;
                    }
                } else if (!this$label.equals(other$label)) {
                    return false;
                }

                Object this$tenantId = this.getTenantId();
                Object other$tenantId = other.getTenantId();
                if (this$tenantId == null) {
                    if (other$tenantId != null) {
                        return false;
                    }
                } else if (!this$tenantId.equals(other$tenantId)) {
                    return false;
                }

                Object this$thumbnail = this.getThumbnail();
                Object other$thumbnail = other.getThumbnail();
                if (this$thumbnail == null) {
                    if (other$thumbnail != null) {
                        return false;
                    }
                } else if (!this$thumbnail.equals(other$thumbnail)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof OssFile;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $size = this.getSize();
        result = result * 59 + ($size == null ? 43 : $size.hashCode());
        Object $id = this.getId();
        result = result * 59 + ($id == null ? 43 : $id.hashCode());
        Object $fileName = this.getFileName();
        result = result * 59 + ($fileName == null ? 43 : $fileName.hashCode());
        Object $bucketName = this.getBucketName();
        result = result * 59 + ($bucketName == null ? 43 : $bucketName.hashCode());
        Object $module = this.getModule();
        result = result * 59 + ($module == null ? 43 : $module.hashCode());
        Object $filePath = this.getFilePath();
        result = result * 59 + ($filePath == null ? 43 : $filePath.hashCode());
        Object $createTime = this.getCreateTime();
        result = result * 59 + ($createTime == null ? 43 : $createTime.hashCode());
        Object $fileSize = this.getFileSize();
        result = result * 59 + ($fileSize == null ? 43 : $fileSize.hashCode());
        Object $fileType = this.getFileType();
        result = result * 59 + ($fileType == null ? 43 : $fileType.hashCode());
        Object $fileLink = this.getFileLink();
        result = result * 59 + ($fileLink == null ? 43 : $fileLink.hashCode());
        Object $label = this.getLabel();
        result = result * 59 + ($label == null ? 43 : $label.hashCode());
        Object $tenantId = this.getTenantId();
        result = result * 59 + ($tenantId == null ? 43 : $tenantId.hashCode());
        Object $thumbnail = this.getThumbnail();
        result = result * 59 + ($thumbnail == null ? 43 : $thumbnail.hashCode());
        return result;
    }
}
