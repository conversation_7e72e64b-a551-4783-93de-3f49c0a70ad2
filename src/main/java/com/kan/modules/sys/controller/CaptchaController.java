package com.kan.modules.sys.controller;

import com.kan.common.utils.Result;
import com.kan.core.api.ApiRest;
import com.kan.core.api.controller.BaseController;
import com.kan.modules.sys.dto.MongoUser;
import com.kan.modules.sys.dto.SysUserLoginDTO;
import com.kan.modules.sys.dto.SysUserLoginReqDTO;
import com.kan.modules.sys.form.BaseFile;
import com.kan.modules.sys.service.CaptchaService;
import com.kan.modules.sys.service.MongoUserService;
import com.kan.modules.sys.utils.BeanCopyUtil;
import com.kan.modules.sys.utils.FileNameDto;
import com.kan.modules.sys.service.OssTemplate;
import com.kan.modules.sys.utils.SystemThreadLocal;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 验证码控制器
 */
@Slf4j
@Api(value = "验证码管理", tags = "验证码管理")
@RestController
@RequestMapping("/exam/api/sys/user")
public class CaptchaController extends BaseController {

    @Autowired(required = false)
    private CaptchaService captchaService;
    @Autowired
    private MongoUserService mongoUserService;
    @Resource
    OssTemplate ossTemplate;

    @Autowired
    private MongoTemplate mongoTemplate;
    @ApiOperation(value = "文件上传", notes = "文件上传")
    @PostMapping("/fileUpload")
    public Result fileUpload(@RequestPart("file") MultipartFile file) {
        SystemThreadLocal.set("label", "默认");
        BaseFile source = null;
        try {
            source = this.ossTemplate.putFile("jvs-form-design", "/jvs-ui/form", file.getOriginalFilename(), file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        FileNameDto target = (FileNameDto) BeanCopyUtil.copy(source, FileNameDto.class);
        target.setOriginalFileName(file.getOriginalFilename());
        target.setFileLink(this.ossTemplate.fileLink(target.getFileName(), target.getBucketName()));
        target.setFileSize(source.getSize());
        return new Result().appOk(target);
    }
    /**
     * 获取验证码
     */
    @CrossOrigin
    @ApiOperation(value = "获取验证码", notes = "获取图形验证码")
    @RequestMapping(value = "/get-captcha", method = {RequestMethod.GET, RequestMethod.POST})
    public void captcha(HttpServletRequest request, HttpServletResponse response) throws IOException {
        captchaService.generateCaptcha(request, response);
    }
    /**
     * MongoDB用户登录
     * @param reqDTO 登录请求参数
     * @return 登录响应
     */
    @CrossOrigin
    @ApiOperation(value = "MongoDB用户登录", notes = "基于MongoDB存储的用户登录，支持验证码验证")
    @RequestMapping(value = "/mongo-login", method = {RequestMethod.POST})
    public Result mongoLogin(@RequestBody SysUserLoginReqDTO reqDTO) {
        SysUserLoginDTO respDTO = mongoUserService.login(reqDTO.getUsername(), reqDTO.getPassword(), reqDTO.getCaptcha(), reqDTO.getKey());
        return new Result().appOk(respDTO);
    }
    /**
     * 测试MongoDB用户查询
     * @param username 用户账户
     * @return 用户信息
     */
    @CrossOrigin
    @ApiOperation(value = "测试MongoDB用户查询", notes = "用于调试MongoDB用户查询问题")
    @RequestMapping(value = "/test-mongo-user", method = {RequestMethod.GET})
    public ApiRest testMongoUser(@RequestParam String username) {
        try {
            MongoUser user = mongoUserService.findByYongHuZhangHu(username);
            if (user != null) {
                return super.success(user);
            } else {
                return super.failure("未找到用户: " + username);
            }
        } catch (Exception e) {
            return super.failure("查询异常: " + e.getMessage());
        }
    }
}
