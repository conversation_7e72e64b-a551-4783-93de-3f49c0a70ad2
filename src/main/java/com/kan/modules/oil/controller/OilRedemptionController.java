package com.kan.modules.oil.controller;

import com.kan.common.utils.Result;
import com.kan.core.api.dto.BaseIdRespDTO;
import com.kan.modules.oil.dto.OilRedemptionTimeGroupDTO;
import com.kan.modules.oil.entity.OilRedemptions;
import com.kan.modules.oil.service.OilRedemptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
@Slf4j
@Api(tags = {"油品赎回"})
@RestController
@RequestMapping("/exam/api/oil/redemption")
public class OilRedemptionController {
    @Autowired
    private OilRedemptionService oilRedemptionService;

    /**
     * 新增油品赎回预约
     * @param reqDTO 请求参数
     * @return 预约ID
     */
    @ApiOperation(value = "新增油品赎回预约", notes = "创建新的油品赎回预约申请")
    @PostMapping("/save")
    @CrossOrigin
    public Result save(@RequestBody OilRedemptions reqDTO) {
        try {
            // 保存预约
            String reservationId = oilRedemptionService.save(reqDTO);

            log.info("油品赎回预约创建成功，预约ID: {}", reservationId);
            return new Result().appOk(new BaseIdRespDTO(reservationId));

        } catch (Exception e) {
            log.error("新增油品赎回预约失败", e);
            return new Result().appOk("新增油品赎回预约失败: " + e.getMessage());
        }
    }

    /**
     * 查询油品赎回预约详情
     * @param id 预约ID
     * @return 预约详情
     */
    @ApiOperation(value = "查询油品赎回预约详情", notes = "根据预约ID查询预约详情")
    @GetMapping("/detail/{id}")
    @CrossOrigin
    public Result detail(@PathVariable("id") String id) {
        try {
            OilRedemptions respDTO = oilRedemptionService.findById(id);
            if (respDTO == null) {
                return new Result().appOk("预约信息不存在");
            }

            return new Result().appOk(respDTO);

        } catch (Exception e) {
            log.error("查询油品赎回预约详情失败，ID: {}", id, e);
            return new Result().appOk("查询预约详情失败: " + e.getMessage());
        }
    }
    /**
     * 查询油品赎回预约列表
     * @return 预约列表
     */
    @ApiOperation(value = "查询油品赎回预约详情", notes = "根据预约ID查询预约详情")
    @GetMapping("/getList/{user_id}/{subTime}")
    @CrossOrigin
    public Result getList(@PathVariable("user_id") String id,@PathVariable("subTime") String subTime) {
        List<OilRedemptions> respDTO = oilRedemptionService.selectList(id,subTime);
        return new Result().appOk(respDTO);
    }

    /**
     * 撤回油品赎回预约
     * @param id 预约ID
     * @return 操作结果
     */
    @ApiOperation(value = "撤回油品赎回预约", notes = "撤回指定的油品赎回预约申请")
    @GetMapping("/withdraw/{id}")
    @CrossOrigin
    public Result withdraw(@PathVariable("id") String id) {
        try {
            boolean success = oilRedemptionService.withdraw(id);
            if (success) {
                log.info("油品赎回预约撤回成功，预约ID: {}", id);
                return new Result().appOk("撤回成功");
            } else {
                return new Result().appOk("撤回失败");
            }

        } catch (Exception e) {
            log.error("撤回油品赎回预约失败，ID: {}", id, e);
            return new Result().appOk("撤回失败: " + e.getMessage());
        }
    }

    /**
     * 按提交时间分组查询用户的油品赎回申请
     * @param userId 用户ID
     * @return 按时间分组的申请列表
     */
    @ApiOperation(value = "按时间分组查询申请", notes = "查询指定用户的油品赎回申请，按提交时间分组并汇总审批状态")
    @GetMapping("/timeGroup/{userId}")
    @CrossOrigin
    public Result getTimeGroupList(@PathVariable("userId") String userId) {
        try {
            List<OilRedemptionTimeGroupDTO> result = oilRedemptionService.getTimeGroupList(userId);
            log.info("查询用户{}的时间分组列表成功，共{}个时间组", userId, result.size());
            return new Result().appOk(result);

        } catch (Exception e) {
            log.error("查询用户{}的时间分组列表失败", userId, e);
            return new Result().appOk("查询失败: " + e.getMessage());
        }
    }

    /**
     * 批量审批指定时间的油品赎回申请
     * @param userId 用户ID
     * @param tiJiaoShiJian 提交时间
     * @return 审批结果
     */
    @ApiOperation(value = "批量审批申请", notes = "批量审批指定用户在指定时间提交的所有待审批申请")
    @GetMapping("/batchApprove")
    @CrossOrigin
    public Result batchApprove(@RequestParam("userId") String userId,
                              @RequestParam("tiJiaoShiJian") String tiJiaoShiJian) {
        try {
            int approvedCount = oilRedemptionService.batchApprove(userId, tiJiaoShiJian);
            String message = String.format("批量审批成功，共审批了%d条记录", approvedCount);
            log.info("批量审批成功，用户ID: {}, 提交时间: {}, 审批记录数: {}", userId, tiJiaoShiJian, approvedCount);
            return new Result().appOk(message);

        } catch (Exception e) {
            log.error("批量审批失败，用户ID: {}, 提交时间: {}", userId, tiJiaoShiJian, e);
            return new Result().appOk("批量审批失败: " + e.getMessage());
        }
    }

}
