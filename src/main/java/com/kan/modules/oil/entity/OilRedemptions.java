package com.kan.modules.oil.entity;

import com.kan.modules.datav.dto.FileDto;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 油品兑换实体类
 * 存储到MongoDB中
 */
@Data
@Document(collection = "444d5f916bfda0c7d4199fdde00aee5eed167c")
public class OilRedemptions implements Serializable {

    private static final long serialVersionUID = 1L;

    @MongoId
    private ObjectId _id;

    /**
     * 客户名称
     */
    @Field("keHuMingChen")
    private String keHuMingChen;

    /**
     * 客户ID
     */
    @Field("keHuid")
    private String keHuid;

    /**
     * 油品种类
     */
    @Field("youPinZhongLei")
    private String youPinZhongLei;

    /**
     * 订购时间
     */
    @Field("dingGouShiJian")
    private String dingGouShiJian;

    /**
     * 单价
     */
    @Field("danJia")
    private BigDecimal danJia;

    /**
     * 赎油数量
     */
    @Field("shuYouShuLiang")
    private BigDecimal shuYouShuLiang;

    /**
     * 赎油金额
     */
    @Field("shuYouJinE")
    private BigDecimal shuYouJinE;
    /**
     * 转账截图
     */
    @Field("zhuanZhangJieTu")
    private List<FileDto> zhuanZhangJieTu;
    /**
     * 客户审批状态
     */
    @Field("keHuShenPiZhuangTai")
    private String keHuShenPiZhuangTai;

    /**
     * 流程进度
     */
    @Field("liuChengJinDu")
    private String liuChengJinDu;

    /**
     * 提交时间
     */
    @Field("tiJiaoShiJian")
    private String tiJiaoShiJian;

    /**
     * 数据ID
     */
    @Field("dataId")
    private String dataId;

    /**
     * ID
     */
    @Field("id")
    private String id;

    /**
     * 模型ID
     */
    @Field("modelId")
    private String modelId;

    /**
     * 更新时间
     */
    @Field("updateTime")
    private String updateTime;

    /**
     * 更新人
     */
    @Field("updateBy")
    private String updateBy;

    /**
     * 更新人ID
     */
    @Field("updateById")
    private String updateById;

    /**
     * 删除标志
     */
    @Field("delFlag")
    private Boolean delFlag;

    /**
     * 工作ID
     */
    @Field("jobId")
    private String jobId;

    /**
     * 部门ID列表
     */
    @Field("deptId")
    private List<String> deptId;

    /**
     * 创建人ID
     */
    @Field("createById")
    private String createById;

    /**
     * 创建人
     */
    @Field("createBy")
    private String createBy;

    /**
     * 创建时间
     */
    @Field("createTime")
    private String createTime;
}
