package com.kan.modules.oil.dto;

import lombok.Data;

/**
 * 油品赎回按时间分组DTO
 */
@Data
public class OilRedemptionTimeGroupDTO {
    
    /**
     * 提交时间
     */
    private String tiJiaoShiJian;
    
    /**
     * 客户审批状态（根据该时间下所有记录的状态汇总）
     * 如果所有记录都不为"待审批"，则返回"已审批"
     * 如果存在"待审批"状态的记录，则返回"待审批"
     */
    private String keHuShenPiZhuangTai;
    
    /**
     * 该时间下的记录总数
     */
    private Long count;
    
    /**
     * 该时间下待审批的记录数
     */
    private Long pendingCount;
    
    public OilRedemptionTimeGroupDTO() {
    }
    
    public OilRedemptionTimeGroupDTO(String tiJiaoShiJian, String keHuShenPiZhuangTai, Long count, Long pendingCount) {
        this.tiJiaoShiJian = tiJiaoShiJian;
        this.keHuShenPiZhuangTai = keHuShenPiZhuangTai;
        this.count = count;
        this.pendingCount = pendingCount;
    }
}
