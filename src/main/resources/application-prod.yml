spring:
    redis:
        open: false  # 是否开启redis缓存  true开启   false关闭
        database: 2
        host: r-2vcrwwn86hdjwzvvzf.redis.cn-chengdu.rds.aliyuncs.com
        port: 6379
        password: Jvs@2024    # 密码（默认为空）
        timeout: 100000ms  # 连接超时时长（毫秒）
        jedis:
            pool:
                max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
                max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
                max-idle: 10      # 连接池中的最大空闲连接
                min-idle: 5       # 连接池中的最小空闲连接
    data:
        mongodb:
            host: dds-2vc17c1cc4bd4e441.mongodb.cn-chengdu.rds.aliyuncs.com
            port: 3717
            database: jvs-data
            username: jvs
            password: Jvs@2024
            authentication-database: admin
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        druid:
            driver-class-name: com.mysql.cj.jdbc.Driver
            url: **********************************************************************************************************************************************************************************************************************************************************************
            username: jvs
            password: Jvs@2024
            initial-size: 10
            max-active: 100
            min-idle: 10
            max-wait: 60000
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            #Oracle需要打开注释
            #validation-query: SELECT 1 FROM DUAL
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            stat-view-servlet:
                enabled: true
                url-pattern: /druid/*
                #login-username: admin
                #login-password: admin
            filter:
                stat:
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: false
                wall:
                    config:
                        multi-statement-allow: true
oss:
    accessKeyId: LTAI5tRpbrYbz2zkYxxAX2pt
    accessKeySecret: ******************************
    roleArn: acs:ram::1709232887969008:role/aliyunosstokengeneratorrole
    bucket: eps-file
    region: oss-cn-hangzhou
    name: s3
    endpoint: https://gyczcmn.cqtransit.com
    access-key: miniominio
    secret-key: miniominio
    #声明公共桶，获取地址的时候直接为公有地址
    publicBuckets:
        #公共桶只有一个, 所有的都可以向这个上面传递, 但需要符合规范./项目/租户/功能名xxxx/xxx/xxx/ 三层,/当前年/月/日/文件名
        - jvs-public
fdfs:
    so-timeout: 1500
    connect-timeout: 600
    thumb-image:
        width: 150
        height: 150
    tracker-list:
        - 47.110.181.204:22122
    webServerUrl: http://47.110.181.204:9000/
